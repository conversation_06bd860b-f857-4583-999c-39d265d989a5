# FlowCamp UML Diagrams - Guest & Student Features

## Use Case Diagrams

### Guest User Journey
```mermaid
graph TB
    Guest[Guest User]
    
    %% Authentication Use Cases
    Guest --> UC1[Browse Home Page]
    Guest --> UC2[View Program Details]
    Guest --> UC3[Explore Bootcamp Information]
    Guest --> UC4[Access Free Classes]
    Guest --> UC5[Read Public Testimonials]
    Guest --> UC6[Register Account]
    Guest --> UC7[Login to System]
    Guest --> UC8[Reset Password]
    Guest --> UC9[View Mentor Profiles]
    Guest --> UC10[Browse Course Catalog]
    
    %% System boundaries
    UC1 --> SYS1[Home Page System]
    UC2 --> SYS2[Program Management System]
    UC3 --> SYS2
    UC4 --> SYS3[Free Class System]
    UC5 --> SYS4[Testimonial System]
    UC6 --> SYS5[Authentication System]
    UC7 --> SYS5
    UC8 --> SYS5
    UC9 --> SYS6[Mentor Profile System]
    UC10 --> SYS7[Course Catalog System]
    
    %% Navigation and UI
    Guest --> UC11[Navigate Using Navbar]
    Guest --> UC12[Use Dropdown Menus]
    Guest --> UC13[View Responsive Design]
    
    UC11 --> SYS8[Navigation System]
    UC12 --> SYS8
    UC13 --> SYS8
```

### Student Learning Management
```mermaid
graph TB
    Student[Student User]
    
    %% Class Management
    Student --> UC1[View Dashboard]
    Student --> UC2[Browse Available Classes]
    Student --> UC3[Enroll in Classes]
    Student --> UC4[Track Class Progress]
    Student --> UC5[Access Learning Materials]
    Student --> UC6[Complete Assignments]
    Student --> UC7[Submit Tasks]
    Student --> UC8[View Assignment Grades]
    Student --> UC9[Download Certificates]
    Student --> UC10[View Class Statistics]
    
    %% Learning Flow
    UC1 --> SYS1[Dashboard System]
    UC2 --> SYS2[Class Enrollment System]
    UC3 --> SYS2
    UC4 --> SYS3[Progress Tracking System]
    UC5 --> SYS4[Learning Material System]
    UC6 --> SYS5[Assignment System]
    UC7 --> SYS5
    UC8 --> SYS5
    UC9 --> SYS6[Certificate System]
    UC10 --> SYS7[Statistics System]
    
    %% Class Interaction
    Student --> UC11[View Class Details]
    Student --> UC12[Access Class Materials]
    Student --> UC13[Participate in Leaderboard]
    Student --> UC14[Mark Materials as Read]
    Student --> UC15[Complete Class Modules]
    
    UC11 --> SYS8[Class Detail System]
    UC12 --> SYS4
    UC13 --> SYS9[Leaderboard System]
    UC14 --> SYS4
    UC15 --> SYS3
```

### Student Profile & Settings
```mermaid
graph TB
    Student[Student User]
    
    %% Profile Management
    Student --> UC1[Update Profile Information]
    Student --> UC2[Change Password]
    Student --> UC3[Upload Profile Picture]
    Student --> UC4[Manage Notification Preferences]
    Student --> UC5[View Account Settings]
    Student --> UC6[Delete Account]
    Student --> UC7[Update Contact Information]
    Student --> UC8[Manage Privacy Settings]
    
    %% Profile System Integration
    UC1 --> SYS1[Profile Management System]
    UC2 --> SYS2[Security System]
    UC3 --> SYS3[File Upload System]
    UC4 --> SYS4[Notification System]
    UC5 --> SYS1
    UC6 --> SYS2
    UC7 --> SYS1
    UC8 --> SYS1
    
    %% Testimonial Management
    Student --> UC9[Write Class Testimonials]
    Student --> UC10[Rate Learning Experience]
    Student --> UC11[Submit Feedback]
    Student --> UC12[View Submitted Testimonials]
    Student --> UC13[Edit Testimonial Permissions]
    
    UC9 --> SYS5[Testimonial System]
    UC10 --> SYS5
    UC11 --> SYS6[Feedback System]
    UC12 --> SYS5
    UC13 --> SYS5
    
    %% Data Synchronization
    Student --> UC14[Sync Profile Across Components]
    Student --> UC15[Persist Settings in LocalStorage]
    Student --> UC16[Handle Cross-Tab Updates]
    
    UC14 --> SYS7[Data Sync System]
    UC15 --> SYS8[Storage System]
    UC16 --> SYS7
```

### Student Navigation & UI
```mermaid
graph TB
    Student[Student User]
    
    %% Navigation Features
    Student --> UC1[Use Student Navbar]
    Student --> UC2[Access Profile Dropdown]
    Student --> UC3[View Notifications]
    Student --> UC4[Navigate Between Pages]
    Student --> UC5[Use Breadcrumb Navigation]
    Student --> UC6[Handle Responsive Design]
    Student --> UC7[Logout from System]
    
    %% Navbar System
    UC1 --> SYS1[Student Navigation System]
    UC2 --> SYS2[Profile Dropdown System]
    UC3 --> SYS3[Notification System]
    UC4 --> SYS4[Routing System]
    UC5 --> SYS5[Breadcrumb System]
    UC6 --> SYS6[Responsive UI System]
    UC7 --> SYS7[Authentication System]
    
    %% Notification Management
    Student --> UC8[Mark Notifications as Read]
    Student --> UC9[Clear All Notifications]
    Student --> UC10[Filter Notifications]
    Student --> UC11[View Notification Details]
    Student --> UC12[Receive Real-time Updates]
    
    UC8 --> SYS3
    UC9 --> SYS3
    UC10 --> SYS3
    UC11 --> SYS3
    UC12 --> SYS3
    
    %% UI Interactions
    Student --> UC13[Use Modal Dialogs]
    Student --> UC14[Handle Form Interactions]
    Student --> UC15[Navigate with Active States]
    Student --> UC16[Use Search Functionality]
    Student --> UC17[Handle Loading States]
    
    UC13 --> SYS8[Dialog System]
    UC14 --> SYS9[Form System]
    UC15 --> SYS1
    UC16 --> SYS10[Search System]
    UC17 --> SYS11[Loading System]
```

## Activity Diagrams

### Guest Authentication to Student Dashboard
```mermaid
flowchart TD
    Start([Guest visits site]) --> ViewHome[View Home page]
    ViewHome --> Decision1{Want to login?}
    
    Decision1 -->|No| BrowseGuest[Browse as guest]
    BrowseGuest --> ViewPrograms[View programs]
    ViewPrograms --> ViewBootcamp[View bootcamp info]
    ViewBootcamp --> ViewFreeClass[View free classes]
    ViewFreeClass --> Decision2{Ready to join?}
    Decision2 -->|No| BrowseGuest
    Decision2 -->|Yes| GoToLogin[Navigate to login]
    
    Decision1 -->|Yes| GoToLogin
    GoToLogin --> LoginPage[Load login page]
    LoginPage --> HideNavFooter[Hide navbar and footer]
    HideNavFooter --> ShowLoginForm[Display login form]
    
    ShowLoginForm --> Decision3{Login method?}
    Decision3 -->|Email/Password| EnterCredentials[Enter email and password]
    Decision3 -->|Google OAuth| GoogleAuth[Initiate Google authentication]
    
    EnterCredentials --> ValidateForm[Validate form inputs]
    ValidateForm --> Decision4{Valid inputs?}
    Decision4 -->|No| ShowErrors[Show validation errors]
    ShowErrors --> ShowLoginForm
    Decision4 -->|Yes| SubmitLogin[Submit login request]
    
    GoogleAuth --> GoogleProcess[Process Google OAuth]
    GoogleProcess --> Decision5{OAuth success?}
    Decision5 -->|No| ShowGoogleError[Show Google error]
    ShowGoogleError --> ShowLoginForm
    Decision5 -->|Yes| AuthSuccess[Authentication successful]
    
    SubmitLogin --> SimulateAPI[Simulate API call]
    SimulateAPI --> Decision6{Login success?}
    Decision6 -->|No| ShowLoginError[Show login error]
    ShowLoginError --> ShowLoginForm
    Decision6 -->|Yes| AuthSuccess
    
    AuthSuccess --> RedirectHome[Redirect to Home page]
    RedirectHome --> ShowNavFooter[Show navbar and footer]
    ShowNavFooter --> LoadHomeContent[Load home page content]
    LoadHomeContent --> End([User logged in successfully])
```

### Student Class Management
```mermaid
flowchart TD
    Start([Student accesses class system]) --> LoadDashboard[Load student dashboard]
    LoadDashboard --> InitializeStore[Initialize class store]
    InitializeStore --> LoadFromStorage[Load data from localStorage]
    LoadFromStorage --> DisplayDashboard[Display dashboard statistics]

    DisplayDashboard --> Decision1{What action?}

    Decision1 -->|Browse Classes| NavigateToClasses[Navigate to Available Classes]
    NavigateToClasses --> LoadAvailableClasses[Load available classes data]
    LoadAvailableClasses --> DisplayClassCards[Display class cards with pagination]
    DisplayClassCards --> Decision2{Enroll in class?}
    Decision2 -->|No| DisplayClassCards
    Decision2 -->|Yes| EnrollInClass[Add class to joined classes]
    EnrollInClass --> UpdateLocalStorage[Update localStorage]
    UpdateLocalStorage --> ShowEnrollmentSuccess[Show enrollment success]
    ShowEnrollmentSuccess --> NavigateToAcademy[Navigate to Academy]

    Decision1 -->|View Academy| NavigateToAcademy
    NavigateToAcademy --> LoadJoinedClasses[Load joined classes]
    LoadJoinedClasses --> FilterByStatus[Filter by ongoing/completed]
    FilterByStatus --> DisplayClassProgress[Display class progress cards]

    DisplayClassProgress --> Decision3{Select class?}
    Decision3 -->|No| DisplayClassProgress
    Decision3 -->|Yes| SetCurrentClass[Set current class in store]
    SetCurrentClass --> NavigateToClassDetail[Navigate to class detail]
    NavigateToClassDetail --> LoadClassMaterials[Load class materials]
    LoadClassMaterials --> DisplayClassContent[Display class content]

    DisplayClassContent --> Decision4{What action?}
    Decision4 -->|View Materials| AccessMaterials[Access learning materials]
    Decision4 -->|Check Progress| ViewProgress[View progress tracking]
    Decision4 -->|View Leaderboard| ViewLeaderboard[View class leaderboard]
    Decision4 -->|Complete Class| CompleteClass[Mark class as completed]

    AccessMaterials --> MarkAsRead[Mark materials as read]
    MarkAsRead --> UpdateProgress[Update class progress]
    UpdateProgress --> SyncWithStore[Sync with class store]
    SyncWithStore --> End([Class management completed])

    ViewProgress --> End
    ViewLeaderboard --> End
    CompleteClass --> GenerateCertificate[Generate certificate]
    GenerateCertificate --> End
```

### Assignment Workflow
```mermaid
flowchart TD
    Start([Student accesses assignments]) --> LoadAssignments[Load assignments page]
    LoadAssignments --> GenerateAssignmentData[Generate assignment data from classes]
    GenerateAssignmentData --> FilterAssignments[Apply search and status filters]
    FilterAssignments --> DisplayAssignments[Display assignment cards]

    DisplayAssignments --> Decision1{Select assignment?}
    Decision1 -->|No| DisplayAssignments
    Decision1 -->|Yes| ViewAssignmentDetail[View assignment details]

    ViewAssignmentDetail --> CheckStatus[Check assignment status]
    CheckStatus --> Decision2{Assignment status?}

    Decision2 -->|Not Started| StartAssignment[Start assignment]
    StartAssignment --> AccessMaterial[Access assignment material]
    AccessMaterial --> ReadInstructions[Read assignment instructions]
    ReadInstructions --> WorkOnAssignment[Work on assignment]

    WorkOnAssignment --> Decision3{Ready to submit?}
    Decision3 -->|No| WorkOnAssignment
    Decision3 -->|Yes| SubmitAssignment[Submit assignment]
    SubmitAssignment --> UpdateTaskStatus[Update task status to 'turned_in']
    UpdateTaskStatus --> SaveToStorage[Save to localStorage]
    SaveToStorage --> ShowSubmissionDialog[Show submission confirmation]
    ShowSubmissionDialog --> WaitForReview[Wait for mentor review]

    Decision2 -->|In Progress| ContinueWork[Continue working]
    ContinueWork --> WorkOnAssignment

    Decision2 -->|Submitted| WaitForReview
    WaitForReview --> CheckReviewStatus[Check if reviewed]
    CheckReviewStatus --> Decision4{Reviewed?}
    Decision4 -->|No| WaitForReview
    Decision4 -->|Yes| ViewGrades[View assignment grades]

    ViewGrades --> DisplayScore[Display score and feedback]
    DisplayScore --> UpdateAssignmentRecord[Update assignment record]
    UpdateAssignmentRecord --> Decision5{View feedback?}
    Decision5 -->|Yes| ShowFeedbackDialog[Show detailed feedback]
    Decision5 -->|No| ReturnToList[Return to assignment list]

    ShowFeedbackDialog --> ReturnToList
    ReturnToList --> End([Assignment workflow completed])

    Decision2 -->|Completed| ViewCompletedAssignment[View completed assignment]
    ViewCompletedAssignment --> ViewGrades
```

### Certificate System
```mermaid
flowchart TD
    Start([Student accesses certificates]) --> LoadCertificatesPage[Load certificates page]
    LoadCertificatesPage --> GetCompletedClasses[Get classes with 100% progress]
    GetCompletedClasses --> FilterCertificates[Filter classes with certificates]
    FilterCertificates --> DisplayCertificateCards[Display certificate cards]

    DisplayCertificateCards --> Decision1{Select certificate?}
    Decision1 -->|No| ApplyFilters[Apply search/sort filters]
    ApplyFilters --> DisplayCertificateCards

    Decision1 -->|Yes| OpenCertificateView[Open certificate view modal]
    OpenCertificateView --> LoadCertificateImage[Load certificate image]
    LoadCertificateImage --> Decision2{Image loaded?}

    Decision2 -->|No| ShowImageError[Show image loading error]
    ShowImageError --> DisplayPlaceholder[Display placeholder image]
    DisplayPlaceholder --> ShowCertificateInfo[Show certificate information]

    Decision2 -->|Yes| ShowCertificateInfo
    ShowCertificateInfo --> DisplayStudentName[Display student name]
    DisplayStudentName --> DisplayClassTitle[Display class title]
    DisplayClassTitle --> DisplayCompletionDate[Display completion date]
    DisplayCompletionDate --> ShowDownloadButton[Show download button]

    ShowDownloadButton --> Decision3{Download certificate?}
    Decision3 -->|No| CloseCertificateView[Close certificate view]
    CloseCertificateView --> DisplayCertificateCards

    Decision3 -->|Yes| InitiateDownload[Initiate certificate download]
    InitiateDownload --> CreateDownloadLink[Create download link]
    CreateDownloadLink --> TriggerDownload[Trigger browser download]
    TriggerDownload --> MarkAsDownloaded[Mark certificate as downloaded]
    MarkAsDownloaded --> UpdateDownloadStatus[Update download status in store]
    UpdateDownloadStatus --> ShowDownloadSuccess[Show download success dialog]
    ShowDownloadSuccess --> AutoCloseDialog[Auto-close dialog after 1 second]
    AutoCloseDialog --> CloseCertificateView

    CloseCertificateView --> End([Certificate system completed])
```

### Profile & Notification Management
```mermaid
flowchart TD
    Start([Student accesses settings]) --> LoadSettingsPage[Load settings page]
    LoadSettingsPage --> LoadStoredProfile[Load profile from localStorage]
    LoadStoredProfile --> InitializeForms[Initialize profile and password forms]
    InitializeForms --> DisplaySettingsTabs[Display settings tabs]

    DisplaySettingsTabs --> Decision1{Which tab?}

    Decision1 -->|Profile Tab| ShowProfileForm[Show profile form]
    ShowProfileForm --> Decision2{Update profile?}
    Decision2 -->|No| DisplaySettingsTabs
    Decision2 -->|Yes| ValidateProfileForm[Validate profile form]
    ValidateProfileForm --> Decision3{Valid data?}
    Decision3 -->|No| ShowValidationErrors[Show validation errors]
    ShowValidationErrors --> ShowProfileForm
    Decision3 -->|Yes| SaveProfileData[Save profile to localStorage]
    SaveProfileData --> EmitProfileUpdate[Emit profile update event]
    EmitProfileUpdate --> UpdateNavbarProfile[Update navbar profile display]
    UpdateNavbarProfile --> ShowSuccessToast[Show success toast]
    ShowSuccessToast --> DisplaySettingsTabs

    Decision1 -->|Account Tab| ShowAccountForm[Show account/password form]
    ShowAccountForm --> Decision4{Change password?}
    Decision4 -->|No| DisplaySettingsTabs
    Decision4 -->|Yes| ValidatePasswordForm[Validate password form]
    ValidatePasswordForm --> Decision5{Valid passwords?}
    Decision5 -->|No| ShowPasswordErrors[Show password validation errors]
    ShowPasswordErrors --> ShowAccountForm
    Decision5 -->|Yes| SavePasswordChange[Save password change flag]
    SavePasswordChange --> ShowPasswordSuccess[Show password change success]
    ShowPasswordSuccess --> DisplaySettingsTabs

    %% Profile Picture Upload Flow
    ShowProfileForm --> Decision6{Upload picture?}
    Decision6 -->|Yes| SelectImageFile[Select image file]
    SelectImageFile --> ValidateImageFile[Validate image file]
    ValidateImageFile --> Decision7{Valid image?}
    Decision7 -->|No| ShowImageError[Show image validation error]
    ShowImageError --> ShowProfileForm
    Decision7 -->|Yes| ShowImagePreview[Show image preview]
    ShowImagePreview --> Decision8{Confirm upload?}
    Decision8 -->|No| CancelUpload[Cancel upload]
    CancelUpload --> ShowProfileForm
    Decision8 -->|Yes| SaveImageToStorage[Save image to localStorage]
    SaveImageToStorage --> UpdateAvatarDisplay[Update avatar display]
    UpdateAvatarDisplay --> EmitProfileUpdate

    %% Notification Management
    Start --> LoadNotifications[Load notifications from localStorage]
    LoadNotifications --> GenerateNotifications[Generate notifications from class data]
    GenerateNotifications --> FilterNotifications[Filter cleared notifications]
    FilterNotifications --> SortNotifications[Sort by read status and timestamp]
    SortNotifications --> DisplayNotifications[Display in navbar dropdown]

    DisplayNotifications --> Decision9{Notification action?}
    Decision9 -->|Mark as Read| MarkNotificationRead[Mark notification as read]
    MarkNotificationRead --> SaveNotificationState[Save notification state]
    SaveNotificationState --> UpdateNotificationDisplay[Update notification display]
    UpdateNotificationDisplay --> DisplayNotifications

    Decision9 -->|Clear All| ClearAllNotifications[Clear all notifications]
    ClearAllNotifications --> SaveClearedState[Save cleared state to localStorage]
    SaveClearedState --> HideNotifications[Hide notification dropdown]
    HideNotifications --> End([Profile & notification management completed])

    ShowSuccessToast --> End
    ShowPasswordSuccess --> End
```

## Sequence Diagrams

### Guest Login to Student Dashboard
```mermaid
sequenceDiagram
    participant Guest as Guest User
    participant LoginPage as LoginPage.vue
    participant Router as Vue Router
    participant Auth as Authentication System
    participant Storage as LocalStorage
    participant Home as Home.vue
    participant Navbar as NavbarHome.vue
    participant Footer as Footer.vue

    Guest->>LoginPage: Navigate to /login
    LoginPage->>Router: Route with hideNavbarAndFooter: true
    Router->>LoginPage: Load login page without navbar/footer
    LoginPage->>Guest: Display login form

    Guest->>LoginPage: Enter credentials
    LoginPage->>LoginPage: Validate form inputs

    alt Email/Password Login
        LoginPage->>Auth: Submit login request
        Auth->>Auth: Simulate API call (1.5s delay)
        Auth->>LoginPage: Return authentication result
    else Google OAuth Login
        LoginPage->>Auth: Initiate Google OAuth
        Auth->>Auth: Process Google authentication
        Auth->>LoginPage: Return OAuth result
    end

    alt Authentication Success
        LoginPage->>Storage: Store authentication token
        LoginPage->>Router: Navigate to Home (/)
        Router->>Home: Load home page
        Router->>Navbar: Show navbar (hideNavbarAndFooter: false)
        Router->>Footer: Show footer (hideNavbarAndFooter: false)
        Home->>Guest: Display authenticated home page
    else Authentication Failure
        LoginPage->>Guest: Display error message
        Guest->>LoginPage: Retry login
    end
```

### Student Dashboard Initialization
```mermaid
sequenceDiagram
    participant Student as Student User
    participant Router as Vue Router
    participant Dashboard as StudentDashboard.vue
    participant Layout as StudentLayoutWrapper.vue
    participant NavbarStudent as NavbarStudent.vue
    participant ClassStore as useClassStore
    participant Storage as LocalStorage
    participant ChartJS as Chart.js

    Student->>Router: Navigate to /student/dashboard
    Router->>Dashboard: Load with hideNavbarAndFooter: true
    Dashboard->>Layout: Wrap in StudentLayoutWrapper
    Layout->>NavbarStudent: Load student navbar
    Layout->>Dashboard: Render dashboard content

    Dashboard->>ClassStore: Initialize class store
    ClassStore->>Storage: Load classes from localStorage
    Storage->>ClassStore: Return stored class data
    ClassStore->>Dashboard: Provide class data

    Dashboard->>Dashboard: Set loading state to true
    Dashboard->>Dashboard: Initialize statistics (800ms delay)

    par Load Dashboard Data
        Dashboard->>ClassStore: Get all classes
        ClassStore->>Dashboard: Return classes array
        Dashboard->>Dashboard: Calculate completed classes count
        Dashboard->>Dashboard: Calculate total lessons count
        Dashboard->>Dashboard: Calculate overall progress percentage
    and Load Notifications
        NavbarStudent->>Storage: Load notifications from localStorage
        Storage->>NavbarStudent: Return notification data
        NavbarStudent->>ClassStore: Get class data for notifications
        ClassStore->>NavbarStudent: Return class materials with tasks
        NavbarStudent->>NavbarStudent: Generate notifications from class data
        NavbarStudent->>Storage: Save updated notifications
    and Load User Profile
        NavbarStudent->>Storage: Load user profile data
        Storage->>NavbarStudent: Return profile information
        NavbarStudent->>NavbarStudent: Update user name and avatar
    end

    Dashboard->>Dashboard: Process monthly class data for chart
    Dashboard->>ChartJS: Initialize progress chart
    ChartJS->>Dashboard: Return chart instance

    Dashboard->>Dashboard: Set loading state to false
    Dashboard->>Student: Display dashboard with statistics and chart

    NavbarStudent->>Student: Display navbar with notifications and profile
```

### Class Progress System
```mermaid
sequenceDiagram
    participant Student as Student User
    participant ClassDetail as DetailClass1.vue
    participant ClassStore as useClassStore
    participant Storage as LocalStorage
    participant Material as Learning Material
    participant Dialog as DialogBox.vue
    participant Utils as studentUtils

    Student->>ClassDetail: Access class detail page
    ClassDetail->>ClassStore: Get current class
    ClassStore->>Storage: Load class data from localStorage
    Storage->>ClassStore: Return class information
    ClassStore->>ClassDetail: Provide current class data

    ClassDetail->>ClassDetail: Calculate progress percentage
    ClassDetail->>Student: Display class progress and materials

    Student->>ClassDetail: Click on learning material
    ClassDetail->>Material: Access material content
    Material->>Student: Display material (PPT/Video/Document)

    Student->>Material: Mark material as read
    Material->>ClassStore: Update material read status
    ClassStore->>ClassStore: Mark material as read
    ClassStore->>ClassStore: Calculate new class progress

    alt Progress Calculation
        ClassStore->>ClassStore: Count read materials
        ClassStore->>ClassStore: Count total materials
        ClassStore->>ClassStore: Calculate percentage (read/total * 100)
        ClassStore->>Storage: Save updated progress to localStorage
    end

    ClassStore->>ClassDetail: Notify progress update
    ClassDetail->>ClassDetail: Update progress display
    ClassDetail->>Dialog: Show "Materials Read" dialog
    Dialog->>Student: Display success confirmation
    Dialog->>Dialog: Auto-close after 1 second

    alt Task Completion
        Student->>Material: Complete assignment task
        Material->>ClassStore: Update task status to 'completed'
        ClassStore->>ClassStore: Update task completion in class data
        ClassStore->>Storage: Save task completion to localStorage
        ClassStore->>ClassDetail: Notify task completion
        ClassDetail->>Dialog: Show "Task Complete" dialog
        Dialog->>Student: Display task completion confirmation
    end

    alt Class Completion Check
        ClassStore->>ClassStore: Check if all materials read
        ClassStore->>ClassStore: Check if all tasks completed
        alt Class 100% Complete
            ClassStore->>ClassStore: Update class status to 'completed'
            ClassStore->>ClassStore: Generate completion date
            ClassStore->>Storage: Save completion status
            ClassDetail->>Student: Show certificate download option
        end
    end

    ClassDetail->>Student: Display updated progress and status
```

### Notification Management
```mermaid
sequenceDiagram
    participant Student as Student User
    participant NavbarStudent as NavbarStudent.vue
    participant ClassStore as useClassStore
    participant Storage as LocalStorage
    participant NotificationDropdown as Notification Dropdown
    participant Utils as studentUtils

    NavbarStudent->>Storage: Load existing notifications on mount
    Storage->>NavbarStudent: Return stored notifications
    NavbarStudent->>Storage: Load cleared notifications list
    Storage->>NavbarStudent: Return cleared notification IDs

    NavbarStudent->>ClassStore: Get all classes for notification generation
    ClassStore->>NavbarStudent: Return classes with materials and tasks

    NavbarStudent->>NavbarStudent: Generate notifications from class data
    loop For each class
        NavbarStudent->>NavbarStudent: Check each material for task status
        alt Task Status Changes
            NavbarStudent->>NavbarStudent: Create notification for task completion
            NavbarStudent->>NavbarStudent: Create notification for task review
            NavbarStudent->>NavbarStudent: Create notification for grade received
        end
    end

    NavbarStudent->>NavbarStudent: Filter out cleared notifications
    NavbarStudent->>NavbarStudent: Preserve read status from existing notifications
    NavbarStudent->>Utils: Format notification dates and times
    Utils->>NavbarStudent: Return formatted timestamps

    NavbarStudent->>NavbarStudent: Sort notifications (unread first, then by timestamp)
    NavbarStudent->>Storage: Save updated notifications to localStorage
    NavbarStudent->>Student: Display notification count badge

    Student->>NavbarStudent: Click notification bell icon
    NavbarStudent->>NotificationDropdown: Show notification dropdown
    NotificationDropdown->>Student: Display list of notifications

    alt Mark Single Notification as Read
        Student->>NotificationDropdown: Click on notification
        NotificationDropdown->>NavbarStudent: Mark notification as read
        NavbarStudent->>NavbarStudent: Update notification read status
        NavbarStudent->>Storage: Save updated notification state
        NavbarStudent->>NavbarStudent: Update notification count
        NavbarStudent->>Student: Update badge display
    else Clear All Notifications
        Student->>NotificationDropdown: Click "Clear All"
        NotificationDropdown->>NavbarStudent: Clear all notifications
        NavbarStudent->>NavbarStudent: Add all notification IDs to cleared list
        NavbarStudent->>Storage: Save cleared notifications to localStorage
        NavbarStudent->>NavbarStudent: Empty notifications array
        NavbarStudent->>Storage: Save empty notifications array
        NavbarStudent->>Student: Hide notification badge
        NavbarStudent->>NotificationDropdown: Close dropdown
    end

    NavbarStudent->>Student: Display updated notification state
```

### Profile Settings Synchronization
```mermaid
sequenceDiagram
    participant Student as Student User
    participant Settings as StudentSettings.vue
    participant Storage as LocalStorage
    participant NavbarStudent as NavbarStudent.vue
    participant ProfileEvent as Custom Event System
    participant FileInput as File Input

    Student->>Settings: Navigate to settings page
    Settings->>Storage: Load existing profile data
    Storage->>Settings: Return profile information
    Settings->>Student: Display profile form with current data

    alt Profile Information Update
        Student->>Settings: Update profile fields (name, email, etc.)
        Settings->>Settings: Validate form data
        Settings->>Storage: Save updated profile to localStorage
        Settings->>ProfileEvent: Dispatch 'userProfileUpdated' event
        ProfileEvent->>NavbarStudent: Receive profile update event
        NavbarStudent->>NavbarStudent: Update displayed user name
        NavbarStudent->>Student: Refresh navbar display
        Settings->>Student: Show success toast message
    end

    alt Profile Picture Upload
        Student->>Settings: Click upload profile picture
        Settings->>FileInput: Open file selection dialog
        FileInput->>Student: Show file picker
        Student->>FileInput: Select image file
        FileInput->>Settings: Return selected file
        Settings->>Settings: Validate image file (type, size)
        alt Valid Image
            Settings->>Settings: Create image preview URL
            Settings->>Student: Show image preview
            Student->>Settings: Confirm upload
            Settings->>Storage: Save image data to localStorage
            Settings->>Settings: Update profile avatar property
            Settings->>ProfileEvent: Dispatch profile update event
            ProfileEvent->>NavbarStudent: Update avatar display
            NavbarStudent->>Student: Show new avatar in navbar
        else Invalid Image
            Settings->>Student: Show validation error message
        end
    end

    alt Password Change
        Student->>Settings: Switch to account tab
        Settings->>Student: Display password change form
        Student->>Settings: Enter current and new passwords
        Settings->>Settings: Validate password requirements
        alt Valid Passwords
            Settings->>Storage: Save password change flag
            Settings->>Student: Show password change success
        else Invalid Passwords
            Settings->>Student: Show validation errors
        end
    end

    alt Cross-Tab Synchronization
        Storage->>Settings: Storage change event (from another tab)
        Settings->>Settings: Parse updated profile data
        Settings->>Settings: Update local profile state
        Settings->>Student: Refresh settings display
    end

    alt Account Deletion
        Student->>Settings: Click delete account
        Settings->>Student: Show confirmation modal
        Student->>Settings: Confirm deletion
        Settings->>Storage: Clear all user data from localStorage
        Settings->>Storage: Clear class data
        Settings->>Storage: Clear notification data
        Settings->>Student: Redirect to login page
    end
```
