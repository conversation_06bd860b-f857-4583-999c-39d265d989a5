<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300">
    <div class="relative">
      <img :src="classData.image" :alt="classData.title" class="w-full h-48 object-cover">
    </div>
    <div class="p-6 relative">
      <h3 class="text-xl font-bold text-gray-900 mb-4">{{ classData.title }}</h3>
      <div class="flex items-center gap-4 text-black mb-6">
        <div class="flex items-center">
          <img src="/calendar.png" alt="Calendar" class="h-5 w-5 mr-2">
          <span class="text-sm">{{ classData.date }}</span>
        </div>
        <div class="flex items-center">
          <img src="/clock.png" alt="Clock" class="h-5 w-5 mr-2">
          <span class="text-sm">{{ classData.time }}</span>
        </div>
      </div>
      <div class="flex justify-between items-center">
        <button
          @click="handleRegister"
          class="group inline-flex items-center justify-center gap-2 bg-teal text-white px-6 py-2.5 rounded-lg font-medium text-sm hover:bg-teal-dark transform transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
        >
          <span>Register</span>
          <span class="transition-transform duration-200 group-hover:translate-x-1">&#x2192;</span>
        </button>

        <button
          @click="handleArrowClick"
          class="w-9 h-9 flex items-center justify-center rounded-full border-2 border-gray-200 hover:border-gray-400 hover:shadow-sm transform hover:scale-105 transition-all duration-300 ease-in-out group cursor-pointer focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
        >
          <span class="text-xl text-gray-400 group-hover:text-gray-600 transition-colors duration-300">&#x203A;</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

defineProps({
  classData: {
    type: Object,
    required: true
  }
});

const router = useRouter();

const handleRegister = () => {
  router.push('/register');
};

const handleArrowClick = () => {
  router.push('/register');
};
</script>