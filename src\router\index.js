import { createWebHistory, createRouter } from "vue-router";
import Home from "@/views/Home.vue";
import ProgramDetail from "@/views/ProgramDetail.vue";
import BootcampPage from "@/views/BootcampPage.vue";
import DetailClass1 from "@/views/DetailClass1.vue";
import Register from "@/views/Register.vue";
import ForgotPassword from "@/views/ForgotPassword.vue";
import AllClassesMentor from "@/views/AllClassesMentor.vue";
import AcademyHome from "@/views/AcademyHome.vue";
import FreeClassSeeMore from "@/views/FreeClassSeeMore.vue";
import AcademyList from "@/views/AcademyList.vue";
import LoginPage from "@/views/LoginPage.vue";
import FreeClass from "@/views/FreeClass.vue";
import Testimonials from "@/views/Testimonials.vue";
import LearningMaterials from "@/views/LearningMaterials.vue";
import AcademyClassStudied from "@/views/AcademyClassStudied.vue";
import AvailableClasses from "@/views/AvailableClasses.vue";
import ClassDetail from "@/views/ClassDetail.vue";
import StudentLeaderboard from "@/views/StudentLeaderboard.vue";
import StudentDashboard from "@/views/StudentDashboard.vue";
import StudentAssignments from "@/views/StudentAssignments.vue";
import StudentStatistics from "@/views/StudentStatistics.vue";
import StudentCertificates from "@/views/StudentCertificates.vue";
import StudentTestimonials from "@/views/StudentTestimonials.vue";
import StudentSettings from "@/views/StudentSettings.vue";
import StudentHelp from "@/views/StudentHelp.vue";
import NotFound from "@/views/NotFound.vue";
import EmailVerification from "@/views/EmailVerification.vue";
import EmailCode from "@/views/EmailCode.vue";
import LearnMore from "@/views/LearnMore.vue";

const routes = [
  {
    path: "/",
    name: "Home",
    component: Home,
  },
  {
    path: "/program",
    name: "Program",
    component: ProgramDetail,
  },
  {
    path: "/program/:id",
    name: "ProgramDetail",
    component: ProgramDetail,
  },
  {
    path: "/bootcamp",
    name: "Bootcamp",
    component: BootcampPage,
  },
  {
    path: "/student/academy",
    name: "Academy",
    component: AcademyClassStudied,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/student/classes",
    name: "AvailableClasses",
    component: AvailableClasses,
    meta: { hideNavbarAndFooter: true },
    props: (route) => ({ ...route.query })
  },
  {
    path: "/student/class-detail/:classId",
    name: "ClassDetail",
    component: ClassDetail,
    meta: { hideNavbarAndFooter: true },
    props: true
  },
  {
    path: "/student/class/:classId",
    name: "DetailClass",
    component: DetailClass1,
    meta: { hideNavbarAndFooter: true },
    props: true,
    beforeEnter: (to, _, next) => {
      const { classId } = to.params;
      if (classId) {
        next();
      } else {
        next('/student/academy');
      }
    }
  },
  {
    path: "/register",
    name: "register",
    component: Register,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/email-verification",
    name: "email-verification",
    component: EmailVerification,
    props: (route) => ({ source: route.query.source || 'register' }),
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/email-code",
    name: "email-code",
    component: EmailCode,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/ForgotPassword",
    name: "ForgotPassword",
    component: ForgotPassword,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/student/class/:classId/materials",
    name: "LearningMaterials",
    component: LearningMaterials,
    meta: { hideNavbarAndFooter: true },
    props: true,
    beforeEnter: (to, _, next) => {
      const { classId } = to.params;
      if (classId) {
        next();
      } else {
        next('/student/academy');
      }
    }
  },
  {
    path: '/student/class/:classId/materials/:materialId',
    name: 'DetailLearningMaterials',
    component: () => import('@/views/DetailLearningMaterials.vue'),
    props: true,
    meta: { hideNavbarAndFooter: true },
    beforeEnter: (to, _, next) => {
      const { classId, materialId } = to.params;
      if (classId && materialId) {
        next();
      } else {
        next('/student/academy');
      }
    }
  },
  {
    path: '/student/class/:classId/leaderboard',
    name: 'StudentLeaderboard',
    component: StudentLeaderboard,
    props: true,
    meta: { hideNavbarAndFooter: true },
    beforeEnter: (to, _, next) => {
      const { classId } = to.params;
      if (classId) {
        next();
      } else {
        next('/student/academy');
      }
    }
  },
  {
    path: '/student/class/:classId/task-history/:materialId',
    name: 'DetailTaskHistory',
    component: () => import('@/views/DetailTaskHistory.vue'),
    meta: { hideNavbarAndFooter: true },
    props: true,
    beforeEnter: (to, _, next) => {
      const { classId, materialId } = to.params;
      if (classId && materialId) {
        next();
      } else {
        next('/student/academy');
      }
    }
  },
  {
    path: "/student/dashboard",
    name: "StudentDashboard",
    component: StudentDashboard,
    meta: { hideNavbarAndFooter: true },
  },
  {
    path: "/student/assignments",
    name: "StudentAssignments",
    component: StudentAssignments,
    meta: { hideNavbarAndFooter: true },
  },
  {
    path: "/student/statistics",
    name: "StudentStatistics",
    component: StudentStatistics,
    meta: { hideNavbarAndFooter: true },
  },
  {
    path: "/student/certificates",
    name: "StudentCertificates",
    component: StudentCertificates,
    meta: { hideNavbarAndFooter: true },
  },
  {
    path: "/student/testimonials",
    name: "StudentTestimonials",
    component: StudentTestimonials,
    meta: { hideNavbarAndFooter: true },
  },
  {
    path: "/student/settings",
    name: "StudentSettings",
    component: StudentSettings,
    meta: { hideNavbarAndFooter: true },
  },
  {
    path: "/student/help",
    name: "StudentHelp",
    component: StudentHelp,
    meta: { hideNavbarAndFooter: true },
  },
  {
    path: "/testimonials",
    name: "Testimonials",
    component: Testimonials,
  },
  {
    path: "/freeclass",
    name: "freeClass",
    component: FreeClass,
  },
  {
    path: "/login",
    name: "Login",
    component: LoginPage,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/AllClasses",
    name: "AllClassesMentor",
    component: AllClassesMentor,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/AcademyMentor",
    name: "AcademyMentor",
    component: AcademyList,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: '/ClassProgress/:title',
    name: 'DetailClassProgress',
    component: () => import('@/views/DetailClassProgress.vue'),
    props: true
  },
  {
    path: '/ClassCompleted/:title',
    name: 'DetailClassCompleted',
    component: () => import('@/views/DetailClassCompleted.vue'),
    props: true
  },
  {
    path: '/detail-rating/:title',
    name: 'DetailRating',
    component: () => import('@/views/DetailRating.vue')
  },
  {
    path: '/detail-modul/:title',
    name: 'DetailModul',
    component: () => import('@/views/DetailModul.vue')
  },
  {
    path: "/academyhome",
    name: "AcademyHome",
    component: AcademyHome,
    meta: { hideNavbarAndFooter: true }
  },
  {
    path: "/freeClassSeeMore",
    name: "FreeClassSeeMore",
    component: FreeClassSeeMore,
  },
  {
    path: "/learn-more/:id/:mentorIndex?",
    name: "LearnMore",
    component: LearnMore,
    props: true
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: NotFound,
    meta: { hideNavbarAndFooter: true }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_, __, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0, behavior: "smooth" };
    }
  },
});

export default router;