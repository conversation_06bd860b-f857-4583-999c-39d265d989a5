/**
 * Dummy Available Classes Data
 *
 * This file contains manually defined data for available classes that students can join.
 * These classes are displayed in the Classes Camp page.
 *
 * Data Structure:
 * - id: Unique identifier for the class (should be a number)
 * - title: Descriptive title of the class
 * - rating: Class rating (number between 1-5, can include decimal)
 * - studentsEnrolled: Number of students enrolled in the class
 * - modules: Number of modules in the class
 * - category: Category of the class (should be one of: 'frontend', 'backend', 'mobile')
 * - description: Detailed description of what the class covers
 * - postedDate: Date when the class was posted (format: YYYY-MM-DD)
 * - postedTime: Time when the class was posted (format: HH:MM AM/PM)
 * - imageUrl: Path to the class image (should be a string pointing to an image in the public directory)
 * - status: Status of the class (should be "available" for classes that can be joined)
 */

// Define categories
const CATEGORIES = {
  FRONTEND: 'frontend',
  BACKEND: 'backend',
  MOBILE: 'mobile'
};

// Frontend classes
const frontendClasses = [
  {
    id: 101,
    title: "Advanced JavaScript: Modern ES6+ Features",
    rating: 4.9,
    studentsEnrolled: 120,
    modules: 7,
    category: CATEGORIES.FRONTEND,
    description: "Take your JavaScript skills to the next level with this comprehensive course on modern ES6+ features. Learn about arrow functions, destructuring, async/await, generators, and more. By the end of this course, you'll be writing cleaner, more efficient JavaScript code and be prepared for modern frontend and backend development.",
    postedDate: "2025-05-15",
    postedTime: "09:30 AM",
    imageUrl: "/mentorCard1.png",
    status: "available",
    certificate: "/certificates/certificate-javascript-es6.jpg",
    // Detailed category description
    categoryDescription: "frontend development, including modern JavaScript frameworks, responsive design, and user interface implementation. You'll learn how to create engaging, interactive web applications with clean, maintainable code.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master the fundamentals and advanced concepts of frontend development, with a focus on modern JavaScript. You'll learn through hands-on projects and practical examples that will prepare you for real-world applications.",
      goals: [
        "Master frontend development concepts and user interface design",
        "Build a portfolio of projects to showcase your skills",
        "Gain practical experience through hands-on assignments",
        "Learn industry-standard tools and workflows",
        "Develop problem-solving skills through real-world challenges",
        "Receive feedback from experienced mentors"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Visual Studio Code",
        "Node.js",
        "Figma"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "HTML5 & CSS3 Fundamentals",
        description: "Master frontend development concepts and create responsive web applications in this hands-on module."
      },
      {
        title: "JavaScript Essentials",
        description: "Master frontend development concepts and create responsive web applications in this hands-on module."
      },
      {
        title: "Responsive Design",
        description: "Master frontend development concepts and create responsive web applications in this hands-on module."
      },
      {
        title: "Modern JavaScript Frameworks",
        description: "Master frontend development concepts and create responsive web applications in this hands-on module."
      },
      {
        title: "State Management",
        description: "Master frontend development concepts and create responsive web applications in this hands-on module."
      },
      {
        title: "Performance Optimization",
        description: "Master frontend development concepts and create responsive web applications in this hands-on module."
      },
      {
        title: "Advanced Frontend Techniques",
        description: "Master frontend development concepts and create responsive web applications in this hands-on module."
      }
    ]
  },
  {
    id: 102,
    title: "Web Accessibility: Building Inclusive Websites",
    rating: 4.7,
    studentsEnrolled: 85,
    modules: 5,
    category: CATEGORIES.FRONTEND,
    description: "Learn how to create websites that are accessible to everyone, including people with disabilities. This course covers WCAG guidelines, semantic HTML, ARIA attributes, keyboard navigation, and testing tools. By the end of this course, you'll be able to build websites that comply with accessibility standards and provide a better experience for all users.",
    postedDate: "2025-05-12",
    postedTime: "10:15 AM",
    imageUrl: "/mentorCard3.png",
    status: "available",
    certificate: "/certificates/certificate-web-accessibility.jpg",
    // Detailed category description
    categoryDescription: "frontend development, with a focus on accessibility standards and inclusive design principles. You'll learn how to create websites that can be used by everyone, including people with disabilities.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you understand and implement web accessibility standards. You'll learn through practical examples and real-world scenarios that will prepare you to create inclusive web experiences.",
      goals: [
        "Master accessibility principles and WCAG guidelines",
        "Learn to implement semantic HTML and ARIA attributes",
        "Understand keyboard navigation and focus management",
        "Build a portfolio of accessible web projects",
        "Develop skills in testing and auditing for accessibility",
        "Learn to create inclusive user experiences"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Visual Studio Code",
        "Screen readers (NVDA, VoiceOver)",
        "Accessibility testing tools (Axe, WAVE)"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "Introduction to Web Accessibility",
        description: "Learn the fundamentals of web accessibility and why it matters for all users."
      },
      {
        title: "WCAG Guidelines and Compliance",
        description: "Master the Web Content Accessibility Guidelines (WCAG) and understand how to implement them."
      },
      {
        title: "Semantic HTML and ARIA",
        description: "Learn how to use semantic HTML and ARIA attributes to create accessible web content."
      },
      {
        title: "Keyboard Navigation and Focus Management",
        description: "Master keyboard navigation and focus management techniques for accessibility."
      },
      {
        title: "Testing and Auditing for Accessibility",
        description: "Learn how to test and audit websites for accessibility compliance using various tools and techniques."
      }
    ]
  },
  {
    id: 103,
    title: "React.js: Building Modern User Interfaces",
    rating: 4.8,
    studentsEnrolled: 150,
    modules: 6,
    category: CATEGORIES.FRONTEND,
    description: "Master React.js, the popular JavaScript library for building user interfaces. Learn component-based architecture, state management, hooks, context API, and how to integrate with backend services. By the end of this course, you'll be able to build dynamic, responsive, and maintainable web applications using React.",
    postedDate: "2025-05-20",
    postedTime: "11:00 AM",
    imageUrl: "/class1.png",
    status: "available",
    certificate: "/certificates/certificate-reactjs-modern.jpg",
    // Detailed category description
    categoryDescription: "frontend development with React.js, focusing on component-based architecture and modern UI development. You'll learn to create dynamic, responsive web applications with the most popular JavaScript library for building user interfaces.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master React.js and its ecosystem. You'll learn through hands-on projects and practical examples that will prepare you for real-world application development.",
      goals: [
        "Master React.js fundamentals and component-based architecture",
        "Learn state management with hooks and context API",
        "Build responsive and interactive user interfaces",
        "Integrate React applications with backend services",
        "Implement routing and navigation in single-page applications",
        "Optimize React applications for performance"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Visual Studio Code",
        "Node.js",
        "npm or yarn",
        "React Developer Tools"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "React Fundamentals",
        description: "Learn the core concepts of React.js and how to create your first components."
      },
      {
        title: "State and Props",
        description: "Master state management and component communication in React applications."
      },
      {
        title: "Hooks and Functional Components",
        description: "Learn how to use React hooks to manage state and side effects in functional components."
      },
      {
        title: "Routing and Navigation",
        description: "Implement routing and navigation in single-page applications with React Router."
      },
      {
        title: "State Management with Context API and Redux",
        description: "Master global state management with Context API and Redux for complex applications."
      },
      {
        title: "API Integration and Deployment",
        description: "Learn how to integrate React applications with backend services and deploy them to production."
      }
    ]
  }
];

// Backend classes
const backendClasses = [
  {
    id: 201,
    title: "Node.js Backend Development",
    rating: 4.8,
    studentsEnrolled: 95,
    modules: 6,
    category: CATEGORIES.BACKEND,
    description: "Master server-side JavaScript with Node.js. This comprehensive course covers everything from basic Node.js concepts to building production-ready RESTful APIs and real-time applications. Learn about Express.js, MongoDB integration, authentication, testing, and deployment strategies. By the end of this course, you'll be able to build scalable and maintainable backend applications using Node.js.",
    postedDate: "2025-05-18",
    postedTime: "2:00 PM",
    imageUrl: "/mentorCard4.png",
    status: "available",
    certificate: "/certificates/certificate-nodejs-development.jpg",
    // Detailed category description
    categoryDescription: "backend development, covering server-side programming, API design, database management, and scalable architecture. You'll develop skills in building robust, secure, and efficient server applications using Node.js and its ecosystem.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master Node.js and server-side JavaScript. You'll learn through hands-on projects and practical examples that will prepare you for real-world backend development.",
      goals: [
        "Understand server-side programming and API development",
        "Master Node.js and Express.js framework",
        "Learn database integration with MongoDB",
        "Implement authentication and authorization",
        "Build real-time applications with Socket.io",
        "Deploy and scale Node.js applications"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Visual Studio Code",
        "Node.js",
        "Postman"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "Introduction to Backend Development",
        description: "Learn essential backend concepts and practical applications in this comprehensive module."
      },
      {
        title: "RESTful API Design",
        description: "Learn essential backend concepts and practical applications in this comprehensive module."
      },
      {
        title: "Database Management",
        description: "Learn essential backend concepts and practical applications in this comprehensive module."
      },
      {
        title: "Authentication & Security",
        description: "Learn essential backend concepts and practical applications in this comprehensive module."
      },
      {
        title: "Performance Optimization",
        description: "Learn essential backend concepts and practical applications in this comprehensive module."
      },
      {
        title: "Deployment & Scaling",
        description: "Learn essential backend concepts and practical applications in this comprehensive module."
      }
    ]
  },
  {
    id: 202,
    title: "Python Django: Web Development",
    rating: 4.6,
    studentsEnrolled: 110,
    modules: 7,
    category: CATEGORIES.BACKEND,
    description: "Learn web development with Python and Django, a high-level web framework that encourages rapid development and clean, pragmatic design. This course covers models, views, templates, forms, authentication, and deployment. By the end of this course, you'll be able to build robust web applications using Django's powerful features.",
    postedDate: "2025-05-22",
    postedTime: "3:30 PM",
    imageUrl: "/mentorCard5.png",
    status: "available",
    certificate: "/certificates/certificate-python-django.jpg",
    // Detailed category description
    categoryDescription: "backend development with Python and Django, focusing on rapid development and clean design. You'll learn to build robust web applications using Django's powerful features and Python's elegant syntax.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master Python web development with Django. You'll learn through hands-on projects and practical examples that will prepare you for real-world application development.",
      goals: [
        "Understand server-side programming and API development",
        "Master Python and Django framework",
        "Learn the MVT (Model-View-Template) architecture",
        "Implement authentication and authorization",
        "Build database-driven web applications",
        "Deploy Django applications to production"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Visual Studio Code",
        "Python 3.8+",
        "Django",
        "PostgreSQL"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "Python Fundamentals for Web Development",
        description: "Learn essential Python concepts for web development with Django."
      },
      {
        title: "Django Basics and Project Structure",
        description: "Understand Django's architecture and create your first Django project."
      },
      {
        title: "Models and Database Integration",
        description: "Learn how to define models and work with databases in Django."
      },
      {
        title: "Views and Templates",
        description: "Master Django's views and templates for rendering dynamic content."
      },
      {
        title: "Forms and User Input",
        description: "Learn how to handle forms and user input in Django applications."
      },
      {
        title: "Authentication and Authorization",
        description: "Implement user authentication and permission systems in Django."
      },
      {
        title: "Deployment and Production",
        description: "Learn how to deploy Django applications to production environments."
      }
    ]
  },
  {
    id: 203,
    title: "RESTful API Design and Development",
    rating: 4.7,
    studentsEnrolled: 80,
    modules: 5,
    category: CATEGORIES.BACKEND,
    description: "Master the principles of RESTful API design and development. Learn how to create scalable, maintainable, and secure APIs that follow REST principles. This course covers resource modeling, HTTP methods, status codes, authentication, documentation, and testing. By the end of this course, you'll be able to design and implement professional-grade APIs.",
    postedDate: "2025-05-25",
    postedTime: "1:45 PM",
    imageUrl: "/mentorCard6.png",
    status: "available",
    certificate: "/certificates/certificate-restful-api.jpg",
    // Detailed category description
    categoryDescription: "backend development with a focus on API design and implementation. You'll learn to create scalable, maintainable, and secure APIs that follow REST principles and best practices.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master RESTful API design and development. You'll learn through hands-on projects and practical examples that will prepare you for creating professional-grade APIs.",
      goals: [
        "Understand server-side programming and API development",
        "Master RESTful principles and best practices",
        "Learn resource modeling and URL design",
        "Implement authentication and authorization for APIs",
        "Create comprehensive API documentation",
        "Test and optimize API performance"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Visual Studio Code",
        "Postman",
        "Swagger/OpenAPI",
        "Git"
      ],
      additionalRequirements: []
    },
    // Module data for this class
    moduleData: [
      {
        title: "Introduction to API Design",
        description: "Learn the fundamentals of API design and RESTful principles."
      },
      {
        title: "Resource Modeling and URL Design",
        description: "Master resource modeling and URL design for RESTful APIs."
      },
      {
        title: "HTTP Methods and Status Codes",
        description: "Learn how to use HTTP methods and status codes correctly in your APIs."
      },
      {
        title: "Authentication and Security",
        description: "Implement secure authentication and authorization for your APIs."
      },
      {
        title: "Documentation and Testing",
        description: "Create comprehensive API documentation and implement testing strategies."
      }
    ]
  }
];

// Mobile classes
const mobileClasses = [
  {
    id: 301,
    title: "Flutter Mobile App Development",
    rating: 4.9,
    studentsEnrolled: 130,
    modules: 8,
    category: CATEGORIES.MOBILE,
    description: "Learn to build beautiful, natively compiled applications for mobile, web, and desktop from a single codebase with Flutter. This course covers Dart programming language, Flutter widgets, state management, navigation, and integration with backend services. By the end of this course, you'll be able to create cross-platform mobile applications with Flutter.",
    postedDate: "2025-05-28",
    postedTime: "10:00 AM",
    imageUrl: "/bootcampHero.png",
    status: "available",
    certificate: "/certificates/certificate-flutter-mobile.jpg",
    // Detailed category description
    categoryDescription: "mobile app development, exploring cross-platform solutions with Flutter. You'll learn to create responsive, feature-rich applications that provide excellent user experiences across different devices using a single codebase.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master Flutter and Dart for cross-platform mobile development. You'll learn through hands-on projects and practical examples that will prepare you for real-world app development.",
      goals: [
        "Learn mobile app development for iOS and Android platforms",
        "Master Dart programming language fundamentals",
        "Build UI with Flutter widgets and Material Design",
        "Implement state management in Flutter applications",
        "Integrate with backend services and APIs",
        "Publish applications to app stores"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Android Studio / Visual Studio Code",
        "Flutter SDK",
        "Emulator/Simulator"
      ],
      additionalRequirements: [
        "Smartphone - Android or iOS device for testing"
      ]
    },
    // Module data for this class
    moduleData: [
      {
        title: "Mobile Development Fundamentals",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "Dart Programming Language",
        description: "Learn the Dart programming language, which is used to build Flutter applications."
      },
      {
        title: "Flutter Widgets and UI",
        description: "Master Flutter widgets and build beautiful user interfaces for your applications."
      },
      {
        title: "Navigation & State Management",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "Data Persistence & API Integration",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "Native Device Features",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "Testing and Debugging",
        description: "Learn how to test and debug Flutter applications for optimal performance."
      },
      {
        title: "App Store Deployment",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      }
    ]
  },
  {
    id: 302,
    title: "React Native: Cross-Platform Mobile Development",
    rating: 4.7,
    studentsEnrolled: 115,
    modules: 7,
    category: CATEGORIES.MOBILE,
    description: "Master React Native to build mobile applications for iOS and Android using JavaScript and React. This course covers components, navigation, state management, native modules, and deployment. By the end of this course, you'll be able to create high-performance, cross-platform mobile applications with a native feel.",
    postedDate: "2025-05-30",
    postedTime: "2:30 PM",
    imageUrl: "/class2.png",
    status: "available",
    certificate: "/certificates/certificate-react-native.jpg",
    // Detailed category description
    categoryDescription: "mobile app development using React Native, a framework for building native apps using React and JavaScript. You'll learn to create cross-platform mobile applications that have the performance of native apps with the development efficiency of web applications.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master React Native for cross-platform mobile development. You'll learn through hands-on projects and practical examples that will prepare you for real-world app development.",
      goals: [
        "Learn mobile app development for iOS and Android platforms",
        "Master React Native fundamentals and components",
        "Implement navigation and routing in mobile apps",
        "Manage state in React Native applications",
        "Integrate with native modules and APIs",
        "Deploy applications to app stores"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Visual Studio Code",
        "Node.js",
        "React Native CLI",
        "Emulator/Simulator"
      ],
      additionalRequirements: [
        "Smartphone - Android or iOS device for testing"
      ]
    },
    // Module data for this class
    moduleData: [
      {
        title: "Mobile Development Fundamentals",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "React Native Basics",
        description: "Learn the fundamentals of React Native and how it differs from React for web."
      },
      {
        title: "UI Components & Layouts",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "Navigation & State Management",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "Data Persistence & API Integration",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "Native Device Features",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      },
      {
        title: "App Store Deployment",
        description: "Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module."
      }
    ]
  },
  {
    id: 303,
    title: "iOS Development with Swift",
    rating: 4.8,
    studentsEnrolled: 90,
    modules: 6,
    category: CATEGORIES.MOBILE,
    description: "Learn iOS app development using Swift, Apple's powerful and intuitive programming language. This course covers Swift fundamentals, UIKit, SwiftUI, Core Data, networking, and app deployment. By the end of this course, you'll be able to build professional iOS applications and publish them to the App Store.",
    postedDate: "2025-06-02",
    postedTime: "11:15 AM",
    imageUrl: "/class3.png",
    status: "available",
    certificate: "/certificates/certificate-ios-swift.jpg",
    // Detailed category description
    categoryDescription: "mobile app development for iOS using Swift, Apple's modern programming language. You'll learn to create native iOS applications with beautiful interfaces and smooth performance that follow Apple's design guidelines.",
    // Targets and goals specific to this class
    targetsAndGoals: {
      description: "This class is designed to help you master iOS development with Swift. You'll learn through hands-on projects and practical examples that will prepare you for real-world app development.",
      goals: [
        "Learn mobile app development for iOS platforms",
        "Master Swift programming language fundamentals",
        "Build UI with UIKit and SwiftUI",
        "Implement data persistence with Core Data",
        "Integrate with Apple's frameworks and APIs",
        "Publish applications to the App Store"
      ]
    },
    // Learning tools required for this class
    learningTools: {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: [
        "Google Collaboratory",
        "Xcode",
        "Swift",
        "iOS Simulator"
      ],
      additionalRequirements: [
        "Smartphone - iOS device for testing (recommended)",
        "Mac computer for Xcode (required)"
      ]
    },
    // Module data for this class
    moduleData: [
      {
        title: "Swift Programming Fundamentals",
        description: "Learn the Swift programming language, which is used to build iOS applications."
      },
      {
        title: "UI Components & Layouts",
        description: "Explore mobile app development techniques and build applications for iOS platforms in this interactive module."
      },
      {
        title: "Navigation & State Management",
        description: "Explore mobile app development techniques and build applications for iOS platforms in this interactive module."
      },
      {
        title: "Data Persistence & API Integration",
        description: "Explore mobile app development techniques and build applications for iOS platforms in this interactive module."
      },
      {
        title: "Native Device Features",
        description: "Explore mobile app development techniques and build applications for iOS platforms in this interactive module."
      },
      {
        title: "App Store Deployment",
        description: "Explore mobile app development techniques and build applications for iOS platforms in this interactive module."
      }
    ]
  }
];

// Combine all classes into one array
export const dummyAvailableClassesData = [
  ...frontendClasses,
  ...backendClasses,
  ...mobileClasses
];

// Export categories and class arrays for direct access
export const categories = CATEGORIES;
export const classesByCategory = {
  frontend: frontendClasses,
  backend: backendClasses,
  mobile: mobileClasses
};

export default dummyAvailableClassesData;
